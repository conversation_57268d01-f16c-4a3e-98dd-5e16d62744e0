const { callLLM } = require("../../llm/llmFactory");
const prisma = require("../../../database/prisma/getPrismaClient");
const { createOpenAIHash } = require("./generateOpenAIHash");
const Bottleneck = require("bottleneck");

// Rate limiting for AI API calls
const aiLimiter = new Bottleneck({
  minTime: 100, // Minimum time between requests (100ms)
  maxConcurrent: 5, // Maximum concurrent requests
});

// Default prompt template for website finding
const DEFAULT_PROMPT = `You are an expert at finding company websites using web search. Your task is to find the official website(s) for an Amazon seller based on the provided information.

**Instructions:**
1. Use the web search tool to find the official website of the seller
2. Search using the seller name, business name, and address if provided
3. Look for official company websites, not marketplace listings
4. Verify the website matches the seller information
5. Return multiple websites if you find several legitimate options
6. If no website is found, return an empty array

**Input Information:**
- Seller Name: {{sellerName}}
- Business Name: {{businessName}}
- Address: {{address}}
- Seller ID: {{sellerId}}
- Amazon Seller URL: {{sellerUrl}}

**Output Format:**
Return a JSON object with this exact structure:
{
  "websites": ["website1.com", "website2.com"],
  "success": true/false,
  "reasoning": "Brief explanation of findings"
}

**Important:**
- Only return legitimate business websites
- Exclude social media profiles, marketplaces, and directories
- If uncertain, mark success as false
- Provide clear reasoning for your findings`;

// Pricing for gpt-4o-mini (per million tokens)
const PRICING = {
  input: 0.25,  // $0.25 per million input tokens
  output: 0.75, // $0.75 per million output tokens
};

/**
 * Calculate cost based on token usage
 */
function calculateCost(inputTokens, outputTokens) {
  const inputCost = (inputTokens / 1000000) * PRICING.input;
  const outputCost = (outputTokens / 1000000) * PRICING.output;
  return inputCost + outputCost;
}

/**
 * Create cache key for OpenAI request
 */
function createCacheKey(sellerData, prompt) {
  const cacheData = {
    sellerName: sellerData.sellerName,
    businessName: sellerData.businessName,
    address: sellerData.address,
    prompt: prompt.substring(0, 500), // First 500 chars of prompt for cache key
  };
  return createOpenAIHash(JSON.stringify(cacheData));
}

/**
 * Generate prompt with seller data
 */
function generatePrompt(sellerData, customPrompt = null) {
  const prompt = customPrompt || DEFAULT_PROMPT;
  
  return prompt
    .replace(/\{\{sellerName\}\}/g, sellerData.sellerName || "Not provided")
    .replace(/\{\{businessName\}\}/g, sellerData.businessName || "Not provided")
    .replace(/\{\{address\}\}/g, sellerData.address || "Not provided")
    .replace(/\{\{sellerId\}\}/g, sellerData.sellerId || "Not provided")
    .replace(/\{\{sellerUrl\}\}/g, sellerData.sellerUrl || "Not provided");
}

/**
 * Make AI request with web search using LLM factory
 */
async function makeOpenAIRequest(sellerData, customPrompt = null, model = "gpt-4o-mini") {
  const prompt = generatePrompt(sellerData, customPrompt);
  const cacheKey = createCacheKey(sellerData, prompt);

  // Check cache first
  const cached = await prisma.openAI_Cache.findUnique({
    where: { hash: cacheKey }
  });

  if (cached) {
    return {
      response: cached.apiResponse,
      inputTokens: cached.inputTokens,
      outputTokens: cached.outputTokens,
      totalCost: cached.totalCost,
      model: cached.model,
      fromCache: true
    };
  }

  // Use LLM factory for AI request with web search
  const llmResult = await callLLM({
    model: model,
    messages: [
      {
        role: "user",
        content: prompt
      }
    ],
    enableWebSearch: true,
    temperature: 0.1,
    maxTokens: 1000,
    metadata: {
      sellerName: sellerData.sellerName,
      businessName: sellerData.businessName,
      useCase: "website_finding"
    }
  });

  if (!llmResult.success) {
    throw new Error(`AI request failed: ${llmResult.error}`);
  }

  const inputTokens = llmResult.usage.inputTokens;
  const outputTokens = llmResult.usage.outputTokens;
  const totalCost = llmResult.usage.cost;

  let response;
  try {
    // Parse the response to extract websites
    response = JSON.parse(llmResult.content);
  } catch (error) {
    // If parsing fails, create a structured response
    response = {
      websites: [],
      success: false,
      reasoning: "Failed to parse AI response: " + error.message
    };
  }

  // Cache the result
  await prisma.openAI_Cache.create({
    data: {
      hash: cacheKey,
      prompt: prompt,
      apiResponse: response,
      inputTokens: inputTokens,
      outputTokens: outputTokens,
      totalCost: totalCost,
      model: model
    }
  });

  return {
    response,
    inputTokens,
    outputTokens,
    totalCost,
    model: model,
    fromCache: false
  };
}

/**
 * Process a single lead with OpenAI
 */
async function processOpenAILead(lead, customPrompt = null) {
  try {
    const sellerData = {
      sellerName: lead.sellerName,
      businessName: lead.businessName,
      address: lead.address,
      sellerId: lead.metadata?.sellerId,
      sellerUrl: lead.sellerUrl
    };

    const result = await aiLimiter.schedule(() =>
      makeOpenAIRequest(sellerData, customPrompt)
    );

    // Update lead status and store result
    await prisma.lead.update({
      where: { id: lead.id },
      data: {
        apiResponse: result.response,
        status: "openai_scraped"
      }
    });

    // Store usage data
    await prisma.openAI_Usage.create({
      data: {
        leadId: lead.id,
        jobId: lead.jobId,
        sellerName: lead.sellerName,
        businessName: lead.businessName,
        address: lead.address,
        sellerId: sellerData.sellerId,
        sellerUrl: lead.sellerUrl,
        searchPriority: lead.metadata?.searchPriority,
        prompt: result.fromCache ? "From cache" : generatePrompt(sellerData, customPrompt),
        response: result.response,
        websites: result.response.websites || [],
        success: result.response.success || false,
        inputTokens: result.inputTokens,
        outputTokens: result.outputTokens,
        totalCost: result.totalCost,
        model: result.model
      }
    });

    return result;
  } catch (error) {
    console.error("Error processing OpenAI lead:", error);
    
    // Update lead status to failed
    await prisma.lead.update({
      where: { id: lead.id },
      data: { status: "openai_failed" }
    });

    // Store error in usage data
    await prisma.openAI_Usage.create({
      data: {
        leadId: lead.id,
        jobId: lead.jobId,
        sellerName: lead.sellerName,
        businessName: lead.businessName,
        address: lead.address,
        sellerId: lead.metadata?.sellerId,
        sellerUrl: lead.sellerUrl,
        searchPriority: lead.metadata?.searchPriority,
        prompt: "Error occurred",
        response: { error: error.message },
        websites: [],
        success: false,
        inputTokens: 0,
        outputTokens: 0,
        totalCost: 0.0,
        model: "gpt-4o-mini",
        errorMessage: error.message
      }
    });

    throw error;
  }
}

/**
 * Process multiple leads in batches
 */
async function processOpenAIBatch(leads, customPrompt = null) {
  const results = [];
  
  for (const lead of leads) {
    try {
      const result = await processOpenAILead(lead, customPrompt);
      results.push(result);
    } catch (error) {
      console.error(`Failed to process lead ${lead.id}:`, error);
      results.push({ error: error.message, leadId: lead.id });
    }
  }

  return results;
}

module.exports = {
  makeOpenAIRequest,
  processOpenAILead,
  processOpenAIBatch,
  generatePrompt,
  calculateCost,
  DEFAULT_PROMPT
};
