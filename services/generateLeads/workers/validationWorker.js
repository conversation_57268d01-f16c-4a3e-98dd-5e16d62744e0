const prisma = require("../../../database/prisma/getPrismaClient");

// Import validation methods
const {
    validateAddress,
} = require("../../generateLeads/validaterMethods/validateAddress");
const {
    validateImage,
} = require("../../generateLeads/validaterMethods/validateImage");
const validateHtmlText = require("../../generateLeads/validaterMethods/validateHtmlText");
const { getSellerImage } = require("../../../utils/getSellerImage");
const { checkHtml } = require("../matchMethods/checkHtml");
const { validateDiscoveredPages } = require("../urlDiscovery/validatePages");
const { scrapePages } = require("../urlDiscovery/scrapePages");
const {
    removeSpecialCharacters,
} = require("../../../utils/removeSpecialCharacters");
// Environment configuration for enabling/disabling validators
const DISABLE_TEXT_VALIDATION = process.env.DISABLE_TEXT_VALIDATION === "true";
const DISABLE_IMAGE_VALIDATION =
    process.env.DISABLE_IMAGE_VALIDATION === "true";
const DISABLE_ADDRESS_VALIDATION =
    process.env.DISABLE_ADDRESS_VALIDATION === "true";
const DISABLE_NUMBER_VALIDATION =
    process.env.DISABLE_NUMBER_VALIDATION === "true";
const DISABLE_EMAIL_VALIDATION =
    process.env.DISABLE_EMAIL_VALIDATION === "true";

/**
 * Base Validator Class
 * Defines the interface and common functionality for all validators
 */
class BaseValidator {
    constructor(name) {
        this.name = name;
        this.resultField = `${name}Validation`;
        this.matchField = `has${name.charAt(0).toUpperCase() + name.slice(1)}Match`;
        this.isEnabled = true;
    }

    /**
     * Check if this validator should be disabled based on environment variables
     */
    checkIfDisabled() {
        const disableFlag = {
            text: DISABLE_TEXT_VALIDATION,
            image: DISABLE_IMAGE_VALIDATION,
            address: DISABLE_ADDRESS_VALIDATION,
            number: DISABLE_NUMBER_VALIDATION,
            email: DISABLE_EMAIL_VALIDATION,
        }[this.name];

        return disableFlag === true;
    }

    /**
     * Validate a URL with provided data
     * @param {string} url - URL to validate
     * @param {object} data - Data needed for validation
     * @returns {object} Validation result
     */
    async validate(url, data) {
        if (this.checkIfDisabled()) {
            console.log(`[VALIDATION] ${this.name} validation is disabled`);
            return { result: null, isMatch: false };
        }

        try {
            const validationResult = await this._runValidation(url, data);
            return validationResult;
        } catch (error) {
            console.error(`[VALIDATION] ${this.name} Validation Error:`, error);
            return {
                result: { error: error.message },
                isMatch: false,
            };
        }
    }

    /**
     * Run the actual validation (to be implemented by subclasses)
     * @param {string} url - URL to validate
     * @param {object} data - Data needed for validation
     * @returns {object} Validation result
     */
    async _runValidation(url, data) {
        throw new Error("_runValidation must be implemented by subclasses");
    }
}

/**
 * TextValidator - Validates text content of a page
 */
class TextValidator extends BaseValidator {
    constructor() {
        super("text");
    }

    async _runValidation(url, data) {
        if (!data.keywords) {
            return { result: null, isMatch: false };
        }

        const result = await validateHtmlText(url, data.keywords);
        if (result && result.prompt) delete result.prompt;

        return {
            result,
            isMatch:
                result &&
                result.message &&
                removeSpecialCharacters(result.message) === "true",
        };
    }
}

/**
 * ImageValidator - Validates images on a page
 */
class ImageValidator extends BaseValidator {
    constructor() {
        super("image");
    }

    async _runValidation(url, data) {
        if (!data.sellerLogo) {
            return { result: null, isMatch: false };
        }

        const result = await validateImage(data.sellerLogo, url, data.keywords);
        if (result && result.prompt) delete result.prompt;

        return {
            result,
            isMatch:
                result &&
                result.message &&
                removeSpecialCharacters(result.message) === "true",
        };
    }
}

/**
 * AddressValidator - Validates address on a page
 */
class AddressValidator extends BaseValidator {
    constructor() {
        super("address");
    }

    async _runValidation(url, data) {
        if (!data.leadAddress) {
            return { result: null, isMatch: false };
        }

        const organicData = { originalUrl: url };
        const result = await validateAddress(organicData, data.leadAddress);

        return {
            result,
            isMatch: result && result.finalScore && result.finalScore > 7,
        };
    }
}

/**
 * NumberValidator - Validates phone numbers on a page
 */
class NumberValidator extends BaseValidator {
    constructor() {
        super("number");
    }

    async _runValidation(url, data) {
        if (!data.leadNumbers || !data.leadNumbers.length) {
            return { result: null, isMatch: false };
        }

        // Ensure leadNumbers is always an array
        const numbersToCheck = Array.isArray(data.leadNumbers) 
            ? data.leadNumbers 
            : (typeof data.leadNumbers === 'string' ? [data.leadNumbers] : []);

        if (numbersToCheck.length === 0) {
            console.log(`[VALIDATION] No valid numbers to check`);
            return { result: null, isMatch: false };
        }

        // Filter out any non-string phone numbers
        const validNumbers = numbersToCheck.filter(number => typeof number === 'string' && number.trim().length > 0);
        
        if (validNumbers.length === 0) {
            console.log(`[VALIDATION] No valid phone number strings found in data`);
            return { result: null, isMatch: false };
        }

        console.log(`[VALIDATION] Checking for ${validNumbers.length} phone numbers on ${url}`);
        
        const organicData = { originalUrl: url };
        const result = await checkHtml(organicData, validNumbers, true);

        const isMatch = result && result.matchedKeywords && result.matchedKeywords.length > 0;
        
        console.log(
            `[VALIDATION] Number validation result for ${url}: ${isMatch ? 'MATCHED' : 'NO MATCH'}, ` +
            `matched ${result && result.matchedKeywords ? result.matchedKeywords.length : 0} phone numbers`
        );
        
        if (isMatch && result.matchedKeywords) {
            console.log(`[VALIDATION] Matched phone numbers: ${JSON.stringify(result.matchedKeywords)}`);
        }

        return {
            result,
            isMatch
        };
    }
}

/**
 * EmailValidator - Validates emails on a page
 */
class EmailValidator extends BaseValidator {
    constructor() {
        super("email");
    }

    async _runValidation(url, data) {
        // Make sure we have valid email data
        if (!data.leadEmails) {
            console.log(`[VALIDATION] No lead emails provided for validation`);
            return { result: null, isMatch: false };
        }

        // Ensure leadEmails is always an array
        const emailsToCheck = Array.isArray(data.leadEmails) 
            ? data.leadEmails 
            : (typeof data.leadEmails === 'string' ? [data.leadEmails] : []);

        if (emailsToCheck.length === 0) {
            console.log(`[VALIDATION] No valid emails to check`);
            return { result: null, isMatch: false };
        }

        // Filter out any non-string emails
        const validEmails = emailsToCheck.filter(email => typeof email === 'string' && email.trim().length > 0);
        
        if (validEmails.length === 0) {
            console.log(`[VALIDATION] No valid email strings found in data`);
            return { result: null, isMatch: false };
        }

        console.log(`[VALIDATION] Checking for ${validEmails.length} emails on ${url}`);
        
        const organicData = { originalUrl: url };
        const result = await checkHtml(
            organicData,
            validEmails,
            true,
            "lowered_spaced"
        );

        const isMatch = result && result.matchedKeywords && result.matchedKeywords.length > 0;
        
        console.log(
            `[VALIDATION] Email validation result for ${url}: ${isMatch ? 'MATCHED' : 'NO MATCH'}, ` +
            `matched ${result && result.matchedKeywords ? result.matchedKeywords.length : 0} emails`
        );
        
        if (isMatch && result.matchedKeywords) {
            console.log(`[VALIDATION] Matched emails: ${JSON.stringify(result.matchedKeywords)}`);
        }

        return {
            result,
            isMatch
        };
    }
}

/**
 * Validator Factory - Provides singleton instances of validators
 */
class ValidatorFactory {
    constructor() {
        this.validators = {};
    }

    /**
     * Get a validator instance
     * @param {string} type - Type of validator to get
     * @returns {BaseValidator} Validator instance
     */
    getValidator(type) {
        if (!this.validators[type]) {
            switch (type) {
                case "text":
                    this.validators[type] = new TextValidator();
                    break;
                case "image":
                    this.validators[type] = new ImageValidator();
                    break;
                case "address":
                    this.validators[type] = new AddressValidator();
                    break;
                case "number":
                    this.validators[type] = new NumberValidator();
                    break;
                case "email":
                    this.validators[type] = new EmailValidator();
                    break;
                default:
                    throw new Error(`Unknown validator type: ${type}`);
            }
        }

        return this.validators[type];
    }
}

// Create singleton instance of ValidatorFactory
const validatorFactory = new ValidatorFactory();

const pageTypeConfig = {
    text: ["home"],
    image: ["home"],
    address: ["home", "about", "contact", "privacy", "terms"],
    number: ["home", "about", "contact", "privacy", "terms"],
    email: ["home", "about", "contact", "privacy", "terms"],
};
/**
 * Runs validation for a specific validator type on a URL
 * @param {string} validatorType - The type of validator to run (text, image, address, email, number)
 * @param {string} url - The URL to validate
 * @param {object} validationData - Data needed for validations
 * @returns {object} Validation result with isMatch flag
 */
async function runPageTypeValidations(validatorType, url, validationData) {
    console.log(
        `[VALIDATION] Running ${validatorType} validation for URL: ${url}`
    );

    try {
        // Get validator instance
        const validator = validatorFactory.getValidator(validatorType);
        console.log(`[VALIDATION] Running ${validator.name} validation on ${url}`);

        // Run validation
        const validationResult = await validator.validate(url, validationData);

        return {
            url: url,
            validatorType: validatorType,
            validations: validationResult,
            isMatch: validationResult && validationResult.isMatch === true,
        };
    } catch (error) {
        console.error(
            `[VALIDATION] Error running ${validatorType} validator:`,
            error
        );
        return {
            url: url,
            validatorType: validatorType,
            validations: { error: error.message },
            isMatch: false,
        };
    }
}

/**
 * Discovers and validates pages for a URL
 * @param {object} url - The LeadUrl object
 * @param {object} lead - The Lead object
 * @returns {object} Validation results
 */
async function discoverAndValidatePages(url, lead) {
    const domain = url.domain;
    const urlString = url.url || url.domain;

    console.log(`[VALIDATION] Discovering pages for URL: ${urlString}`);

    try {
        // Discover pages
        let discoveredPages = url.discoveredPages || {};
        if (!discoveredPages || Object.keys(discoveredPages).length === 0) {
            console.log(`[VALIDATION] Searching for pages`);
            discoveredPages = await scrapePages(url.url, domain);
            await prisma.leadUrl.update({
                where: { id: url.id },
                data: {
                    discoveredPages: discoveredPages,
                },
            });
        }
        console.log(
            `[VALIDATION] Discovered pages: ${JSON.stringify(discoveredPages)}`
        );

        // Initialize results
        let results = {
            discoveredPages,
            textValidation: {},
            imageValidation: {},
            addressValidation: {},
            numberValidation: {},
            emailValidation: {},
            matchedAddressUrls: [],
            matchedNumberUrls: [],
            matchedEmailUrls: [],
            matchedTextUrls: [],
            matchedImageUrls: [],
            hasTextMatch: false,
            hasImageMatch: false,
            hasAddressMatch: false,
            hasNumberMatch: false,
            hasEmailMatch: false,
        };

        // Prepare validation data
        const validationData = {
            keywords: url.keywords,
            sellerLogo: await getSellerImage(lead.sellerUrl),
            leadAddress: lead.address,
            leadNumbers: lead.numbers,
            leadEmails: lead.scraped_emails,
        };

        // create url list from discovered pages and page types
        const discoveredUrls = {};
        for (const [validatorType, pageTypes] of Object.entries(pageTypeConfig)) {
            discoveredUrls[validatorType] = [];
            for (const pageType of pageTypes) {
                if (discoveredPages[pageType]) {
                    const urls =
                        discoveredPages[pageType] instanceof Array
                            ? discoveredPages[pageType]
                            : [discoveredPages[pageType]];
                    console.log(
                        `[VALIDATION] Discovered URLs: ${JSON.stringify(urls)} for page type: ${pageType} and validator: ${validatorType}`
                    );
                    discoveredUrls[validatorType] =
                        discoveredUrls[validatorType].concat(urls);
                }
            }
            discoveredUrls[validatorType] = [
                ...new Set(discoveredUrls[validatorType]),
            ];
        }
        console.log(
            `[VALIDATION] Discovered URLs: ${JSON.stringify(discoveredUrls)}`
        );

        // Run validations for each validator type
        for (const [validatorType, pageUrls] of Object.entries(discoveredUrls)) {
            let validationResults = [];
            let hasMatched = false;
            let matchedUrls = [];
            
            // Check if necessary data for this validator type is available
            const dataAvailable = (function() {
                switch(validatorType) {
                    case "text":
                        return validationData.keywords && Array.isArray(validationData.keywords) && validationData.keywords.length > 0;
                    case "image":
                        return !!validationData.sellerLogo;
                    case "address":
                        return !!validationData.leadAddress;
                    case "number":
                        return validationData.leadNumbers && Array.isArray(validationData.leadNumbers) && validationData.leadNumbers.length > 0;
                    case "email":
                        return validationData.leadEmails && 
                               (Array.isArray(validationData.leadEmails) ? validationData.leadEmails.length > 0 : !!validationData.leadEmails);
                    default:
                        return false;
                }
            })();
            
            if (!dataAvailable) {
                console.log(`[VALIDATION] Skipping ${validatorType} validation because required data is missing`);
                continue;
            }
            
            // Skip if no URLs to validate
            if (pageUrls.length === 0) {
                console.log(`[VALIDATION] Skipping ${validatorType} validation because no URLs were discovered`);
                continue;
            }
            
            console.log(`[VALIDATION] Starting ${validatorType} validation on ${pageUrls.length} URLs`);

            for (const pageUrl of pageUrls) {
                if (hasMatched) break; // Stop at the first match
                
                // Check if this URL has already been matched in previous validations
                // This check was incorrectly trying to use 'in' operator on an array
                const matchedUrlsField = `matched${validatorType.charAt(0).toUpperCase() + validatorType.slice(1)}Urls`;
                const existingMatchedUrls = url[matchedUrlsField] || [];
                const isAlreadyMatched = Array.isArray(existingMatchedUrls) && existingMatchedUrls.includes(pageUrl);
                
                if (isAlreadyMatched) {
                    console.log(
                        `[VALIDATION] Skipping ${pageUrl} because it already matched for ${validatorType}`
                    );
                    hasMatched = true; // Since it already matched, set this flag
                    matchedUrls.push(pageUrl); // Add to our current matches
                    continue;
                }
                
                const validationResult = await runPageTypeValidations(
                    validatorType,
                    pageUrl,
                    validationData
                );
                if (validationResult.isMatch) {
                    hasMatched = true;
                    matchedUrls.push(pageUrl);
                }
                validationResults.push(validationResult.validations);
            }

            // Store the validation results and match status in the appropriate fields
            results[`${validatorType}Validation`] = validationResults;
            results[
                `has${validatorType.charAt(0).toUpperCase() + validatorType.slice(1)}Match`
            ] = hasMatched;

            // Store matched URLs if needed for debugging or reporting purposes
            if (matchedUrls.length > 0) {
                results[
                    `matched${validatorType.charAt(0).toUpperCase() + validatorType.slice(1)}Urls`
                ] = matchedUrls;
            }
        }

        return results;
    } catch (error) {
        console.error(
            `[VALIDATION] Error discovering and validating pages: ${error.message}`
        );
        return {
            discoveredPages: {},
            textValidation: {},
            imageValidation: {},
            addressValidation: {},
            numberValidation: {},
            emailValidation: {},
            matchedAddressUrls: [],
            matchedNumberUrls: [],
            matchedEmailUrls: [],
            matchedTextUrls: [],
            matchedImageUrls: [],
            hasTextMatch: false,
            hasImageMatch: false,
            hasAddressMatch: false,
            hasNumberMatch: false,
            hasEmailMatch: false,
            error: error.message,
        };
    }
}

/**
 * Validates a lead by processing its URLs
 * @param {object} lead - The lead to validate
 */
async function processUrlValidation(lead) {
    console.log(
        `[VALIDATION] Starting URL validation for lead ${lead.id} - ${lead.sellerName}`
    );

    // Find URLs to validate
    const urls = await prisma.leadUrl.findMany({
        where: {
            leadId: lead.id,
            confidence: { gte: 1.5 },
        },
        take: 10,
        orderBy: [{ confidence: "desc" }, { googlePosition: "asc" }],
    });

    if (urls.length === 0) {
        console.log("[VALIDATION] No URLs found for validation.");
        return;
    }

    console.log(
        `[VALIDATION] URLs found for validation: ${urls.length}, lead: ${lead.id}`
    );

    // Process each URL
    for (const url of urls) {
        console.log("--------------------------------------------------");
        console.log(`[VALIDATION] Processing URL: ${url.url || url.domain}`);

        // Discover and validate pages
        const validationResults = await discoverAndValidatePages(url, lead);
        console.log(
            `[VALIDATION] Validation results: ${JSON.stringify(validationResults, null, 2)}`
        );

        // Update the URL with validation results
        try {
            // Check schema first to see which fields actually exist
            const updateData = {};

            // These JSON fields should exist in most schemas
            updateData.textValidation = validationResults.textValidation || {};
            updateData.imageValidation = validationResults.imageValidation || {};
            updateData.addressValidation = validationResults.addressValidation || {};
            updateData.numberValidation = validationResults.numberValidation || {};
            updateData.emailValidation = validationResults.emailValidation || {};
            updateData.discoveredPages = validationResults.discoveredPages || {};
            updateData.hasTextMatch = validationResults.hasTextMatch || false;
            updateData.hasImageMatch = validationResults.hasImageMatch || false;
            updateData.hasAddressMatch = validationResults.hasAddressMatch || false;
            updateData.hasNumberMatch = validationResults.hasNumberMatch || false;
            updateData.hasEmailMatch = validationResults.hasEmailMatch || false;

            // Store match URLs in appropriate fields if they exist in the schema
            if (Array.isArray(validationResults.matchedAddressUrls)) {
                updateData.matchedAddressUrls = validationResults.matchedAddressUrls;
            }
            if (Array.isArray(validationResults.matchedNumberUrls)) {
                updateData.matchedNumberUrls = validationResults.matchedNumberUrls;
            }
            if (Array.isArray(validationResults.matchedEmailUrls)) {
                updateData.matchedEmailUrls = validationResults.matchedEmailUrls;
            }
            if (Array.isArray(validationResults.matchedTextUrls)) {
                updateData.matchedTextUrls = validationResults.matchedTextUrls;
            }
            if (Array.isArray(validationResults.matchedImageUrls)) {
                updateData.matchedImageUrls = validationResults.matchedImageUrls;
            }

            // Add boolean match indicators as custom fields if they're not causing problems
            const existingUrl = await prisma.leadUrl.findUnique({
                where: { id: url.id },
                select: { id: true },
            });

            if (existingUrl) {
                await prisma.leadUrl.update({
                    where: { id: url.id },
                    data: updateData,
                });
                console.log(
                    `[VALIDATION] Successfully updated URL ${url.id} in database`
                );
            }
        } catch (error) {
            console.error(
                `[VALIDATION] Error updating URL ${url.id} in database:`,
                error
            );
        }
        console.log("------------------------------------------------");
    }

    console.log(`[VALIDATION] Completed URL validation for lead ${lead.id}`);
}

module.exports = { processUrlValidation };

/**
 * Main function to test URL validation on a specific lead ID
 */
async function main() {
    console.log("Starting validation worker test run");

    try {
        // Get lead ID from arguments or use the default test ID
        const leadId = process.argv[2] || 67516;
        console.log(`[TEST] Running validation test for lead ID: ${leadId}`);

        // Fetch the lead from database
        const lead = await prisma.lead.findUnique({
            where: { id: parseInt(leadId) },
            include: {
                urls: {
                    take: 5,
                    orderBy: [{ confidence: "desc" }],
                },
            },
        });

        if (!lead) {
            console.error(`[TEST] Lead with ID ${leadId} not found`);
            process.exit(1);
        }

        console.log(`[TEST] Found lead: ${lead.id} - ${lead.sellerName}`);
        console.log(`[TEST] Lead has ${lead.urls.length} URLs`);

        // Process the lead validation
        console.log(`[TEST] Starting validation process`);
        await processUrlValidation(lead);

        console.log(`[TEST] Validation process completed successfully`);

        // Fetch lead again to see updated validation results
        const updatedLead = await prisma.lead.findUnique({
            where: { id: parseInt(leadId) },
        });

        console.log(`[TEST] Validation results:`);
        console.log(JSON.stringify(updatedLead.urls, null, 2));
    } catch (error) {
        console.error(`[TEST] Error during test run: ${error.message}`);
        console.error(error);
    } finally {
        // Close the database connection
        await prisma.$disconnect();
        console.log(`[TEST] Test completed, database disconnected`);
    }
}

// Run the main function if this file is executed directly
if (require.main === module) {
    main().catch((error) => {
        console.error("Error in main function:", error);
        process.exit(1);
    });
}
