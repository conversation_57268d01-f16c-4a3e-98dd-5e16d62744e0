const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();
const axios = require("axios");

require("dotenv").config();
const SMART_LEADS_API_KEY = process.env.SMART_LEADS_API_KEY;

async function saveClientsFromSmartLeads() {
  try {
    const clientUrl = `https://server.smartlead.ai/api/v1/client/?api_key=${SMART_LEADS_API_KEY}`;
    const response = await axios.get(clientUrl);
    const clients = response.data;

    if (!Array.isArray(clients)) {
      console.error("Invalid clients data", clients);
      return;
    }

    const dataToInsert = clients.map((client) => ({
      name: client.name,
      clientId: client.id,
    }));

    await prisma.client.createMany({
      data: dataToInsert,
      skipDuplicates: true,
    });

    console.log("All Clients Added Successfully!");
  } catch (error) {
    console.error(
      "Error fetching clients:",
      error.response?.data || error.message || error
    );
    throw error;
  }
}

// saveClientsFromSmartLeads();

module.exports = { saveClientsFromSmartLeads };
