const BasePreprocessor = require("./basePreprocessor");
const { extractDomain } = require("../../utils/domainHelper");

/**
 * Amazon Seller-specific preprocessor
 */
class AmazonSellerPreprocessor extends BasePreprocessor {
  async afterPrismaTypeCast(transformedData) {
    if (transformedData.domain) {
      transformedData.domain = extractDomain(transformedData.domain);
    }

    return transformedData;
  }
}

module.exports = AmazonSellerPreprocessor;
