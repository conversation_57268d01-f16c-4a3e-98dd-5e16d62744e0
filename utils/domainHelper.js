/*
  Domain extraction using the Public Suffix List when available.
  - Prefer using `tldts` (kept up-to-date with the PSL) for accurate eTLD+1.
  - Falls back to a heuristic that handles common multi-level TLD patterns
    (e.g., co.uk, com.au, us.com) without maintaining an exhaustive list here.

  To enable the accurate path, install:
    npm i tldts --save
*/

let tldts = null;
try {
  // Optional dependency. If not installed, gracefully degrade to the fallback logic.
  // eslint-disable-next-line import/no-extraneous-dependencies, global-require
  tldts = require('tldts');
} catch (_) {
  tldts = null;
}

function isIpAddress(hostname) {
  // IPv4 or IPv6 (very loose check for IPv6)
  return /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname) || /:/.test(hostname);
}

function extractDomain(url) {
  try {
    if (!url) return;
    if (!url.startsWith("http")) {
      url = `https://${url}`;
    }
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname;

    // Preferred: use tldts when available for PSL-accurate extraction
    if (tldts && typeof tldts.getDomain === 'function') {
      const registrable = tldts.getDomain(hostname, { allowPrivateDomains: true });
      // console.log('Registrable:- ', registrable);
      if (registrable) {
        return registrable;
      }
    }

    // Fallback heuristic: handle common multi-level TLD tokens
    // console.log('Fallback Hostname:- ', hostname);
    const parts = hostname.split('.');
    if (parts.length < 2) return hostname;

    // A broad set of tokens used as second-level categories across ccTLDs and
    // CentralNic-style namespaces under .com/.net (co, com, net, org, gov, edu, etc.).
    // This is NOT exhaustive like PSL, but covers common cases until `tldts` is installed.
    const secondLevelTokens = new Set([
      'ac','co','com','net','org','gov','edu','mil','int','nom','ne','or','id','me','sch','plc','ltd',
      // Country codes used as pseudo second-level under .com/.net (e.g., us.com, uk.net)
      'us','uk','de','in','br','au','ca','jp','fr','it','es','ru','mx','nl','se','ch','no','fi','dk',
      'ie','nz','za','pl','tr','ar','at','be','hk','kr','tw','pt','gr','cz','hu','sg','il','my','ro',
      'ua','th','sk','bg','si','lt','lv','ee','vn'
    ]);

    if (secondLevelTokens.has(parts[parts.length - 2])) {
      return parts.slice(-3).join('.');
    }
    return parts.slice(-2).join('.');
  } catch (error) {
    console.error('Invalid URL:', error);
    return "";
  }
}

module.exports = { extractDomain };
// Example usage
// console.log(extractDomain('sub.example.com'));         // Output: example.com
// console.log(extractDomain('https://www.example.co.uk'));       // Output: example.co.uk
// console.log(extractDomain('https://example.com'));             // Output: example.com
// console.log(extractDomain('https://***************'));         // Output: 123.123
// console.log(extractDomain('https://subdomain.example.com.au')); // Output: example.com.au
// console.log(extractDomain('https://localhost:3000'));          // Output: localhost
// console.log(extractDomain('https://invalid-url'));             // Output: null
// console.log(extractDomain(''));                                // Output: null
// console.log(extractDomain('http://www.mzoosleepmask.us.com'));             // Output: example.com
// console.log(extractDomain('https://shopkhim.myshopify.com'));             // Output: shopkhim.myshopify.com
